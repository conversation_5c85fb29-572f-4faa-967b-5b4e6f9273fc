/**
 * CORS middleware for Azure Functions
 * Handles Cross-Origin Resource Sharing headers and preflight requests
 */

import { HttpRequest, HttpResponseInit } from '@azure/functions';

export interface CorsOptions {
  allowedOrigins?: string[];
  allowedMethods?: string[];
  allowedHeaders?: string[];
  allowCredentials?: boolean;
  maxAge?: number;
}

const defaultCorsOptions: CorsOptions = {
  allowedOrigins: [
    'http://localhost:3000',
    'https://hepz.tech',
    'http://127.0.0.1:3000',
    'https://app.hepz.tech',
    'https://portal.azure.com'
  ],
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers',
    'X-Tenant-ID',
    'X-User-ID',
    'X-Correlation-ID',
    'x-correlation-id',
    'X-Request-Time',
    'x-request-time',
    'x-signalr-user-agent',
    'X-SignalR-User-Agent',
    'X-Azure-Functions-Key',
    'x-azure-functions-key',
    'X-Functions-Key',
    'x-functions-key',
    'Cache-Control',
    'Pragma',
    'Expires',
    'If-Modified-Since',
    'If-None-Match',
    'X-Forwarded-For',
    'X-Real-IP',
    'User-Agent',
    'Referer',
    'Connection',
    'Upgrade',
    'Sec-WebSocket-Key',
    'Sec-WebSocket-Version',
    'Sec-WebSocket-Protocol',
    'Sec-WebSocket-Extensions'
  ],
  allowCredentials: true,
  maxAge: 86400 // 24 hours
};

/**
 * Get allowed origins from environment and defaults
 */
function getAllowedOrigins(): string[] {
  const envOrigins = process.env.CORS_ALLOWED_ORIGINS?.split(',').map(o => o.trim()) || [];
  const defaultOrigins = [
    'http://localhost:3000',
    'https://hepz.tech',
    'http://127.0.0.1:3000',
    'https://app.hepz.tech',
    'https://portal.azure.com'
  ];

  return [...new Set([...envOrigins, ...defaultOrigins])];
}

/**
 * Add CORS headers to response
 */
export function addCorsHeaders(
  response: HttpResponseInit,
  request: HttpRequest,
  options: CorsOptions = {}
): HttpResponseInit {
  const corsOptions = { ...defaultCorsOptions, ...options };
  const origin = request.headers.get('origin') || '';

  // Get allowed origins from environment and defaults
  const allowedOrigins = getAllowedOrigins();

  // Determine allowed origin
  let allowedOrigin = 'null';

  if (origin && allowedOrigins.includes(origin)) {
    allowedOrigin = origin;
  } else if (corsOptions.allowedOrigins?.includes('*')) {
    allowedOrigin = origin || '*';
  } else if (corsOptions.allowedOrigins?.includes(origin)) {
    allowedOrigin = origin;
  } else if (allowedOrigins.length > 0) {
    // For development, be more permissive with localhost
    if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
      allowedOrigin = origin;
    }
  }

  const corsHeaders = {
    'Access-Control-Allow-Origin': allowedOrigin,
    'Access-Control-Allow-Methods': corsOptions.allowedMethods?.join(', ') || '',
    'Access-Control-Allow-Headers': corsOptions.allowedHeaders?.join(', ') || '',
    'Access-Control-Max-Age': corsOptions.maxAge?.toString() || '86400',
    'Access-Control-Expose-Headers': 'X-Correlation-ID, X-Request-Time, X-Total-Count',
    ...(corsOptions.allowCredentials && { 'Access-Control-Allow-Credentials': 'true' }),
    'Vary': 'Origin'
  };

  return {
    ...response,
    headers: {
      ...response.headers,
      ...corsHeaders
    }
  };
}

/**
 * Handle preflight OPTIONS request
 */
export function handlePreflight(
  request: HttpRequest,
  options: CorsOptions = {}
): HttpResponseInit | null {
  if (request.method === 'OPTIONS') {
    const response = addCorsHeaders({
      status: 200,
      headers: {
        'Content-Length': '0',
        'Cache-Control': 'max-age=86400'
      }
    }, request, options);

    // Log preflight requests for debugging
    const origin = request.headers.get('origin');
    const requestedMethod = request.headers.get('access-control-request-method');
    const requestedHeaders = request.headers.get('access-control-request-headers');

    console.log('CORS Preflight:', {
      origin,
      requestedMethod,
      requestedHeaders,
      url: request.url
    });

    return response;
  }
  return null;
}

/**
 * CORS middleware wrapper for Azure Functions
 */
export function withCors(
  handler: (request: HttpRequest, context: any) => Promise<HttpResponseInit>,
  options: CorsOptions = {}
) {
  return async (request: HttpRequest, context: any): Promise<HttpResponseInit> => {
    // Handle preflight request
    const preflightResponse = handlePreflight(request, options);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      // Execute the handler
      const response = await handler(request, context);

      // Add CORS headers to response
      return addCorsHeaders(response, request, options);
    } catch (error) {
      // Ensure CORS headers are added even for error responses
      const errorResponse = {
        status: 500,
        jsonBody: { error: 'Internal server error' }
      };

      return addCorsHeaders(errorResponse, request, options);
    }
  };
}

/**
 * Production-ready CORS options for all endpoints
 */
export const productionCorsOptions: CorsOptions = {
  allowedOrigins: getAllowedOrigins(),
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers',
    'X-Tenant-ID',
    'X-User-ID',
    'X-Correlation-ID',
    'x-correlation-id',
    'X-Request-Time',
    'x-request-time',
    'x-signalr-user-agent',
    'X-SignalR-User-Agent',
    'X-Azure-Functions-Key',
    'x-azure-functions-key',
    'X-Functions-Key',
    'x-functions-key',
    'Cache-Control',
    'Pragma',
    'Expires',
    'If-Modified-Since',
    'If-None-Match',
    'X-Forwarded-For',
    'X-Real-IP',
    'User-Agent',
    'Referer',
    'Connection',
    'Upgrade',
    'Sec-WebSocket-Key',
    'Sec-WebSocket-Version',
    'Sec-WebSocket-Protocol',
    'Sec-WebSocket-Extensions'
  ],
  allowCredentials: true,
  maxAge: 86400
};
