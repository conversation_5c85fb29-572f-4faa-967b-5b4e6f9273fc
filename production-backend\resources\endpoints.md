📚 Actual API Endpoints (for documentation):
=============================================

## local runtime backend Functions:

        admin-role-assignments-bulk-assign: [POST,OPTIONS] http://localhost:7071/management/role-assignments/bulk-assign

        admin-role-assignments-bulk-revoke: [POST,OPTIONS] http://localhost:7071/management/role-assignments/bulk-revoke

        admin-role-assignments-create: [POST,OPTIONS] http://localhost:7071/management/role-assignments/create

        admin-role-assignments-delete: [DELETE,OPTIONS] http://localhost:7071/management/role-assignments/{assignmentId}/delete

        admin-role-assignments-list: [GET,OPTIONS] http://localhost:7071/management/role-assignments/list

        admin-role-assignments-update: [PUT,OPTIONS] http://localhost:7071/management/role-assignments/{assignmentId}/update

        admin-roles-delete: [DELETE,OPTIONS] http://localhost:7071/management/roles/{roleId}/delete       

        admin-roles-get: [GET,OPTIONS] http://localhost:7071/management/roles/{roleId}/details

        admin-roles-list: [GET,OPTIONS] http://localhost:7071/management/roles

        admin-roles-update: [PUT,OPTIONS] http://localhost:7071/management/roles/{roleId}/update

        admin-tenants-delete: [DELETE,OPTIONS] http://localhost:7071/management/tenants/{tenantId}/delete

        admin-tenants-list: [GET,OPTIONS] http://localhost:7071/management/tenants

        admin-tenants-update: [PUT,PATCH,OPTIONS] http://localhost:7071/management/tenants/{tenantId}/update

        ai-analytics: [GET,OPTIONS] http://localhost:7071/ai/analytics

        ai-analyze-readability: [POST,OPTIONS] http://localhost:7071/ai/analyze-readability

        ai-batch-process: [POST,OPTIONS] http://localhost:7071/ai/batch/process

        ai-comprehensive-analysis: [POST,OPTIONS] http://localhost:7071/ai/documents/comprehensive-analysis

        ai-content-suggestions: [POST,OPTIONS] http://localhost:7071/ai/content-suggestions

        ai-document-analyze: [POST,OPTIONS] http://localhost:7071/ai/documents/analyze

        ai-extract-key-points: [POST,OPTIONS] http://localhost:7071/ai/extract-key-points

        ai-form-process: [POST,OPTIONS] http://localhost:7071/ai/forms/process

        ai-generate-document: [POST,OPTIONS] http://localhost:7071/ai/generate-document

        ai-generate-outline: [POST,OPTIONS] http://localhost:7071/ai/generate-outline

        ai-improve-content: [POST,OPTIONS] http://localhost:7071/ai/improve-content

        ai-insights: [POST,OPTIONS] http://localhost:7071/ai/insights

        ai-intelligent-search: [GET,POST,OPTIONS] http://localhost:7071/search/intelligent

        ai-model-train: [POST,OPTIONS] http://localhost:7071/ai/models/train

        ai-operation-cancel: [POST,OPTIONS] http://localhost:7071/ai/operations/{operationId}/cancel      

        ai-operation-retry: [POST,OPTIONS] http://localhost:7071/ai/operations/{operationId}/retry        

        ai-operation-start: [POST,OPTIONS] http://localhost:7071/ai/operations

        ai-operation-status: [GET,OPTIONS] http://localhost:7071/ai/operations/{operationId}

        ai-operations-list: [GET,OPTIONS] http://localhost:7071/ai/operations/list

        ai-process: [POST,OPTIONS] http://localhost:7071/ai/process

        ai-summarize: [POST,OPTIONS] http://localhost:7071/ai/summarize[2025-06-15T09:54:44.874Z] {"timestamp":"2025-06-15T09:54:44.846Z","level":"DEBUG","message":"Redis setex operation","key":"eventgrid:published:evt-1749981280800-3b6kt8b","ttl":3600}


[2025-06-15T09:54:45.098Z] {"timestamp":"2025-06-15T09:54:44.846Z","level":"INFO","message":"Event published successfully","eventId":"evt-1749981280800-3b6kt8b","eventType":"Test.HealthCheck","subject":"health/check","attempt":0,"processingTime":4046}
        ai-translate: [POST,OPTIONS] http://localhost:7071/ai/translate

[2025-06-15T09:54:45.104Z] Γ£à Event Grid Integration Service initialized successfully (4046ms)
        analytics-get: [POST,OPTIONS] http://localhost:7071/analytics

[2025-06-15T09:54:45.109Z] ≡ƒîÉ Initializing Event Hub Service with Azure Identity...
        analytics-process: [POST,OPTIONS] http://localhost:7071/analytics/process

[2025-06-15T09:54:45.113Z] {"timestamp":"2025-06-15T09:54:44.849Z","level":"INFO","message":"Analytics Event Hub client initialized","namespace":"hepz-event-hub","hubName":"analytics-events"}
        analytics-realtime: [GET,OPTIONS] http://localhost:7071/analytics/realtime

[2025-06-15T09:54:45.115Z] {"timestamp":"2025-06-15T09:54:44.849Z","level":"INFO","message":"Integration Event Hub client initialized","namespace":"hepz-event-hub","hubName":"integration-events"}
        api-key-create: [POST,OPTIONS] http://localhost:7071/api-keys

[2025-06-15T09:54:45.117Z] {"timestamp":"2025-06-15T09:54:44.850Z","level":"INFO","message":"Event Hub service initialized successfully with Azure Identity"}
        api-key-validate: [POST,OPTIONS] http://localhost:7071/api-keys/validate

[2025-06-15T09:54:45.119Z] Γ£à Event Hub Service initialized successfully (4ms)
        api-keys-create: [POST,OPTIONS] http://localhost:7071/infrastructure/api-keys

[2025-06-15T09:54:45.123Z] ≡ƒÄë Service initialization completed!
        api-keys-list: [GET,OPTIONS] http://localhost:7071/infrastructure/api-keys/list

[2025-06-15T09:54:45.125Z] ≡ƒôè Status: 5/5 services initialized (13221ms total)
        audit-alerts: [GET,OPTIONS] http://localhost:7071/audit/alerts

[2025-06-15T09:54:45.133Z]    Γ£à Redis Enhanced Service: Ready (9169ms)
        audit-event-record: [POST,OPTIONS] http://localhost:7071/security/audit

[2025-06-15T09:54:45.135Z]    Γ£à Service Bus Enhanced Service: Ready (1ms)
        audit-logs: [GET,OPTIONS] http://localhost:7071/audit/logs

[2025-06-15T09:54:45.137Z]    Γ£à SignalR Enhanced Service: Ready (0ms)
        audit-metrics: [GET,OPTIONS] http://localhost:7071/audit/metrics

[2025-06-15T09:54:45.138Z]    Γ£à Event Grid Integration Service: Ready (4046ms)
        auth-login: [POST,OPTIONS] http://localhost:7071/infrastructure/auth/login

        auth-sync: [POST,OPTIONS] http://localhost:7071/auth/sync

[2025-06-15T09:54:45.140Z]    Γ£à Event Hub Service: Ready (4ms)
[2025-06-15T09:54:45.156Z] ∩┐╜ Enhanced features available:
        backup-management: [POST,GET,OPTIONS] http://localhost:7071/system/backups

[2025-06-15T09:54:45.158Z]    ≡ƒö┤ Redis: Distributed locking, pub/sub, session management, clustering    
        cache-management: [POST,OPTIONS] http://localhost:7071/system/cache

[2025-06-15T09:54:45.169Z]    ≡ƒÜî Service Bus: Dead letter handling, circuit breaker, batch processing   
        collaboration-session-create: [POST,OPTIONS] http://localhost:7071/collaboration/sessions

[2025-06-15T09:54:45.171Z]    ≡ƒôí SignalR: Connection management, group management, cross-instance scaling
        collaboration-session-join: [POST,OPTIONS] http://localhost:7071/collaboration/sessions/{sessionId}/join

[2025-06-15T09:54:45.174Z]    ΓÜí Event Grid: Advanced filtering, batching, schema validation
        compliance-assessment: [POST,OPTIONS] http://localhost:7071/security/compliance/assessment        

[2025-06-15T09:54:45.181Z]    ≡ƒîÉ Event Hub: Real-time analytics streaming, Azure Identity authentication
        content-generate: [POST,OPTIONS] http://localhost:7071/ai/content/generate

[2025-06-15T09:54:45.182Z] Γ£¿ HEPZ Enterprise Document Processing Platform is ready!
        dashboard-create: [POST,OPTIONS] http://localhost:7071/dashboards

[2025-06-15T09:54:45.184Z] ≡ƒîÉ All unified functions are registered and available
        dashboard-get: [GET,OPTIONS] http://localhost:7071/dashboards/{dashboardId}

[2025-06-15T09:54:45.185Z] ≡ƒöù Azure services are initialized and connected
        dashboard-metrics: [GET,OPTIONS] http://localhost:7071/dashboard/metrics

[2025-06-15T09:54:45.187Z] ≡ƒôí Event-driven handlers are active and listening  dashboard-recent-activity:
 [GET,OPTIONS] http://localhost:7071/dashboard/recent-activity


        data-decryption: [POST,OPTIONS] http://localhost:7071/storage/encryption/decrypt

[2025-06-15T09:54:45.202Z] ≡ƒÜÇ Production-ready backend is now serving requests
        data-encryption: [POST,OPTIONS] http://localhost:7071/storage/encryption/encrypt

[2025-06-15T09:54:45.203Z] {"timestamp":"2025-06-15T09:54:45.070Z","level":"DEBUG","message":"Redis setex operation","key":"eventgrid:published:evt-1749981279103-702au11","ttl":3600}
        data-export: [POST,OPTIONS] http://localhost:7071/data/export

[2025-06-15T09:54:45.205Z] {"timestamp":"2025-06-15T09:54:45.070Z","level":"INFO","message":"Event published successfully","eventId":"evt-1749981279103-702au11","eventType":"Performance.Alert","subject":"redis/metrics","attempt":0,"processingTime":5967}
        data-migration: [POST,OPTIONS] http://localhost:7071/storage/migration
[2025-06-15T09:54:45.208Z] {"timestamp":"2025-06-15T09:54:45.070Z","level":"INFO","message":"Event published to Event Grid successfully","eventType":"Performance.Alert","subject":"redis/metrics","eventId":"evt-1749981279103-702au11","correlationId":"4daac934-4643-4c36-9b84-fbdacf5a07b0","retryCount":0}

        device-register: [POST,OPTIONS] http://localhost:7071/mobile/devices/register

        device-sync: [POST,OPTIONS] http://localhost:7071/mobile/devices/sync

        document-analytics: [GET,OPTIONS] http://localhost:7071/documents/{documentId}/analytics

        document-analyze: [POST,OPTIONS] http://localhost:7071/documents/analyze

        document-collaboration-end: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/collaboration/end

        document-collaboration-start: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/collaboration/start

        document-comment-delete: [DELETE,OPTIONS] http://localhost:7071/documents/{documentId}/comments/{commentId}/delete

        document-comments-create: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/comments/create

        document-comments-get: [GET,OPTIONS] http://localhost:7071/documents/{documentId}/comments/list   

        document-comments-update: [PUT,OPTIONS] http://localhost:7071/documents/{documentId}/comments/{commentId}/update

        document-download: [GET,OPTIONS] http://localhost:7071/documents/{documentId}/download

        document-enhancement-categories-create: [POST,OPTIONS] http://localhost:7071/document-enhancement/categories

        document-enhancement-categories-list: [GET,OPTIONS] http://localhost:7071/document-enhancement/categories/list

        document-enhancement-operations-list: [GET,OPTIONS] http://localhost:7071/document-enhancement/operations

        document-enhancement-process: [POST,OPTIONS] http://localhost:7071/document-enhancement/process

        document-list: [GET,OPTIONS] http://localhost:7071/documents

        document-metadata-update: [PUT,PATCH,OPTIONS] http://localhost:7071/documents/{documentId}/metadata

        document-process: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/process

        document-process-stop: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/process/stop

        document-retrieve: [GET,OPTIONS] http://localhost:7071/documents/{documentId}

        document-share: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/share

        document-tags-update: [PUT,PATCH,OPTIONS] http://localhost:7071/documents/{documentId}/tags       

        document-update: [PUT,PATCH,OPTIONS] http://localhost:7071/documents/{documentId}/update

        document-upload: [POST,OPTIONS] http://localhost:7071/documents/upload

        document-upload-complete: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/upload/complete

        document-upload-image: [POST,OPTIONS] http://localhost:7071/documents/upload-image

        email-send: [POST,OPTIONS] http://localhost:7071/communication/emails

        event-grid-publish: [POST,OPTIONS] http://localhost:7071/eventgrid/publish

        event-grid-webhook: [POST,OPTIONS] http://localhost:7071/eventgrid/webhook

        external-services-process: [POST,OPTIONS] http://localhost:7071/external-services/process

        feature-flag-evaluate: [GET,OPTIONS] http://localhost:7071/system/feature-flags/evaluate

        feature-flags: [GET,POST,PUT,DELETE,OPTIONS] http://localhost:7071/system/feature-flags

        infrastructure-templates-list: [GET,OPTIONS] http://localhost:7071/infrastructure/templates/list  

        integration-create: [POST,OPTIONS] http://localhost:7071/integrations

        integration-get: [GET,OPTIONS] http://localhost:7071/integrations/{integrationId}

        integration-list: [GET,OPTIONS] http://localhost:7071/integrations/list

        lemonsqueezy-webhook: [POST,OPTIONS] http://localhost:7071/webhooks/lemonsqueezy

        logging-entry: [POST,OPTIONS] http://localhost:7071/infrastructure/logging

        message-send: [POST,OPTIONS] http://localhost:7071/communication/messages

        metrics-collect: [POST,OPTIONS] http://localhost:7071/system/metrics

        mobile-notification-send: [POST,OPTIONS] http://localhost:7071/mobile/notifications/send

        mobile-process: [POST,OPTIONS] http://localhost:7071/mobile/process

        model-train: [POST,OPTIONS] http://localhost:7071/analytics/models/train

        monitoring-alerts: [GET,OPTIONS] http://localhost:7071/monitoring/alerts

        monitoring-health: [GET,OPTIONS] http://localhost:7071/monitoring/health

        monitoring-performance: [GET,OPTIONS] http://localhost:7071/monitoring/performance

        notification-analytics: [GET,OPTIONS] http://localhost:7071/notifications/analytics

        notification-list: [GET,OPTIONS] http://localhost:7071/notifications

        notification-mark-read: [POST,OPTIONS] http://localhost:7071/notifications/mark-read

        notification-preferences: [GET,PUT,PATCH,OPTIONS] http://localhost:7071/notifications/preferences 

        notification-send: [POST,OPTIONS] http://localhost:7071/notifications/send

        notification-tracking: [POST,OPTIONS] http://localhost:7071/notifications/track

        offline-data: [GET,OPTIONS] http://localhost:7071/mobile/offline-data

        organization-create: [POST,OPTIONS] http://localhost:7071/organizations

        organization-get: [GET,OPTIONS] http://localhost:7071/organizations/{organizationId}

        organization-invite-member: [POST,OPTIONS] http://localhost:7071/organizations/{organizationId}/members/invite

[2025-06-15T09:54:46.124Z] Host lock lease acquired by instance ID '000000000000000000000000F2735798'.
        organization-list: [GET,OPTIONS] http://localhost:7071/organizations/list

        organization-member-activities: [GET,OPTIONS] http://localhost:7071/organizations/members/{memberId}/activities

        organization-member-role-update: [PUT,PATCH,OPTIONS] http://localhost:7071/organizations/{organizationId}/members/{userId}/role

        organization-update: [PUT,PATCH,OPTIONS] http://localhost:7071/organizations/{organizationId}/update

        performance-metrics: [GET,OPTIONS] http://localhost:7071/performance/metrics

        prediction-generate: [POST,OPTIONS] http://localhost:7071/analytics/predictions

        project-activity: [GET,OPTIONS] http://localhost:7071/projects/{projectId}/activity

        project-add-member: [POST,OPTIONS] http://localhost:7071/projects/{projectId}/members

        project-create: [POST,OPTIONS] http://localhost:7071/projects

        project-documents: [GET,OPTIONS] http://localhost:7071/projects/{projectId}/documents

        project-get: [GET,OPTIONS] http://localhost:7071/projects/{projectId}

        project-list: [GET,OPTIONS] http://localhost:7071/projects/list

        project-update: [PUT,PATCH,OPTIONS] http://localhost:7071/projects/{projectId}/update

        push-notification-send: [POST,OPTIONS] http://localhost:7071/communication/notifications/push     

        rag-query: [POST,OPTIONS] http://localhost:7071/rag/query

        rate-limit-check: [GET,POST,OPTIONS] http://localhost:7071/infrastructure/rate-limit/check        

        reports-generate: [POST,OPTIONS] http://localhost:7071/reports/generate

        search: [GET,OPTIONS] http://localhost:7071/search

        search-advanced: [GET,POST,OPTIONS] http://localhost:7071/search/advanced

        search-analytics: [POST,OPTIONS] http://localhost:7071/search/analytics

        search-documents: [GET,OPTIONS] http://localhost:7071/search/documents

        search-execute: [POST,OPTIONS] http://localhost:7071/storage/search

        search-index-document: [POST,OPTIONS] http://localhost:7071/storage/search/index

        search-suggestions: [GET,POST,OPTIONS] http://localhost:7071/search/suggestions

        security-dashboard: [GET,OPTIONS] http://localhost:7071/security/dashboard

        security-event-record: [POST,OPTIONS] http://localhost:7071/security/events

        security-operation-status: [GET,OPTIONS] http://localhost:7071/security/operations/{operationId}/status

        security-scan: [POST,OPTIONS] http://localhost:7071/security/scan

        smart-form-process: [POST,OPTIONS] http://localhost:7071/ai/smart-form/process

        smart-form-templates: [GET,OPTIONS] http://localhost:7071/ai/smart-form/templates

        storage-bulk-upload: [POST,OPTIONS] http://localhost:7071/storage/bulk/upload

        storage-configure: [POST,OPTIONS] http://localhost:7071/storage/configure

        storage-operation-status: [GET,OPTIONS] http://localhost:7071/storage/operations/{operationId}/status

        storage-sync: [POST,OPTIONS] http://localhost:7071/storage/sync

        subscription-create: [POST,OPTIONS] http://localhost:7071/subscriptions

        subscription-get: [GET,OPTIONS] http://localhost:7071/subscriptions/{subscriptionId}

        system-configuration: [GET,POST,PUT,DELETE,OPTIONS] http://localhost:7071/system/configuration    

        system-health: [GET,OPTIONS] http://localhost:7071/system/health

        team-member-add: [POST,OPTIONS] http://localhost:7071/teams/{teamId}/members

        team-member-remove: [DELETE,OPTIONS] http://localhost:7071/teams/{teamId}/members/{userId}/remove 

        team-member-update: [PUT,PATCH,OPTIONS] http://localhost:7071/teams/{teamId}/members/{userId}/update

        team-members-list: [GET,OPTIONS] http://localhost:7071/teams/{teamId}/members/list

        template-categories: [GET,OPTIONS] http://localhost:7071/templates/categories

        template-clone: [POST,OPTIONS] http://localhost:7071/templates/{templateId}/clone

        template-create: [POST,OPTIONS] http://localhost:7071/templates/create

        template-delete: [DELETE,OPTIONS] http://localhost:7071/templates/{templateId}/delete

        template-download: [GET,OPTIONS] http://localhost:7071/templates/{templateId}/download

        template-generate: [POST,OPTIONS] http://localhost:7071/templates/generate

        template-get: [GET,OPTIONS] http://localhost:7071/templates/{templateId}

        template-preview: [POST,OPTIONS] http://localhost:7071/templates/{templateId}/preview

        template-share: [POST,OPTIONS] http://localhost:7071/templates/{templateId}/share

        template-sharing-entities: [GET,OPTIONS] http://localhost:7071/templates/sharing/entities

        template-sharing-get: [GET,OPTIONS] http://localhost:7071/templates/{templateId}/sharing

        template-update: [PUT,OPTIONS] http://localhost:7071/templates/{templateId}/update

        templates-create: [POST,OPTIONS] http://localhost:7071/infrastructure/templates

        templates-list: [GET,OPTIONS] http://localhost:7071/templates/list

        user-activity-get: [GET,OPTIONS] http://localhost:7071/users/{userId?}/activity

        user-activity-track: [POST,OPTIONS] http://localhost:7071/users/activity

        user-get: [GET,OPTIONS] http://localhost:7071/users/{userId}

        user-permissions-get: [GET,OPTIONS] http://localhost:7071/users/{userId?}/permissions

        user-preferences-get: [GET,OPTIONS] http://localhost:7071/users/preferences

        user-preferences-reset: [POST,OPTIONS] http://localhost:7071/users/preferences/reset

        user-preferences-update: [PUT,PATCH,OPTIONS] http://localhost:7071/users/preferences/update       

        user-profile-get: [GET,OPTIONS] http://localhost:7071/users/{userId?}/profile

        user-profile-update: [PUT,PATCH,OPTIONS] http://localhost:7071/users/profile

        user-tenant-switch: [POST,OPTIONS] http://localhost:7071/users/tenant/switch

        user-theme-preferences: [PUT,PATCH,OPTIONS] http://localhost:7071/user/preferences/theme

        users-list: [GET,OPTIONS] http://localhost:7071/users

        users-lookup: [POST,OPTIONS] http://localhost:7071/users/lookup

        webhook-deliver: [POST,OPTIONS] http://localhost:7071/webhooks/deliver

        workflow-analytics: [GET,OPTIONS] http://localhost:7071/workflows/analytics

        workflow-create: [POST,OPTIONS] http://localhost:7071/workflows

        workflow-execute: [POST,OPTIONS] http://localhost:7071/workflows/execute

        workflow-execution-status: [GET,OPTIONS] http://localhost:7071/workflows/executions/{executionId}

        workflow-executions: [GET,OPTIONS] http://localhost:7071/workflows/{workflowId}/executions        

        workflow-get: [GET,OPTIONS] http://localhost:7071/workflows/{workflowId}

        workflow-list: [GET,OPTIONS] http://localhost:7071/workflows/list

        workflow-search-suggestions: [GET,OPTIONS] http://localhost:7071/workflows/search/suggestions     

        workflow-template-create: [POST,OPTIONS] http://localhost:7071/workflow-templates

        workflows-list: [GET,OPTIONS] http://localhost:7071/workflows/all

        aiOperations: serviceBusTrigger

        analyticsAggregation: serviceBusTrigger

        cache-warming-scheduler: timerTrigger

        custom-events-trigger: eventGridTrigger

        customEventGridTrigger: eventGridTrigger

        dailyCleanupTimer: timerTrigger

        deadLetterQueue: queueTrigger

        documentBlobTrigger: blobTrigger

        documentCollaboration: serviceBusTrigger

        documentProcessing: serviceBusTrigger

        documentProcessingQueue: queueTrigger

        emailQueue: queueTrigger

        exportBlobTrigger: blobTrigger

        hourlyHealthCheckTimer: timerTrigger

        notificationDelivery: serviceBusTrigger

        notificationQueue: queueTrigger

        scheduledEmails: serviceBusTrigger

        storage-events-trigger: eventGridTrigger

        storageEventGridTrigger: eventGridTrigger

        systemMonitoring: serviceBusTrigger

        templateBlobTrigger: blobTrigger

        weeklyAnalyticsTimer: timerTrigger

        workflowOrchestration: serviceBusTrigger

## Deployed function HTTP routes
Mapped function route '/management/role-assignments/bulk-assign' [POST,OPTIONS] to 'admin-role-assignments-bulk-assign'
Mapped function route '/management/role-assignments/bulk-revoke' [POST,OPTIONS] to 'admin-role-assignments-bulk-revoke'
Mapped function route '/management/role-assignments/create' [POST,OPTIONS] to 'admin-role-assignments-create'
Mapped function route '/management/role-assignments/{assignmentId}/delete' [DELETE,OPTIONS] to 'admin-role-assignments-delete'
Mapped function route '/management/role-assignments/list' [GET,OPTIONS] to 'admin-role-assignments-list'
Mapped function route '/management/role-assignments/{assignmentId}/update' [PUT,OPTIONS] to 'admin-role-assignments-update'
Mapped function route '/management/roles/{roleId}/delete' [DELETE,OPTIONS] to 'admin-roles-delete'
Mapped function route '/management/roles/{roleId}/details' [GET,OPTIONS] to 'admin-roles-get'
Mapped function route '/management/roles' [GET,OPTIONS] to 'admin-roles-list'
Mapped function route '/management/roles/{roleId}/update' [PUT,OPTIONS] to 'admin-roles-update'
Mapped function route '/management/tenants/{tenantId}/delete' [DELETE,OPTIONS] to 'admin-tenants-delete'
Mapped function route '/management/tenants' [GET,OPTIONS] to 'admin-tenants-list'
Mapped function route '/management/tenants/{tenantId}/update' [PUT,PATCH,OPTIONS] to 'admin-tenants-update'
Mapped function route '/ai/analytics' [GET,OPTIONS] to 'ai-analytics'
Mapped function route '/ai/analyze-readability' [POST,OPTIONS] to 'ai-analyze-readability'
Mapped function route '/ai/batch/process' [POST,OPTIONS] to 'ai-batch-process'
Mapped function route '/ai/documents/comprehensive-analysis' [POST,OPTIONS] to 'ai-comprehensive-analysis'
Mapped function route '/ai/content-suggestions' [POST,OPTIONS] to 'ai-content-suggestions'
Mapped function route '/ai/documents/analyze' [POST,OPTIONS] to 'ai-document-analyze'
Mapped function route '/ai/extract-key-points' [POST,OPTIONS] to 'ai-extract-key-points'
Mapped function route '/ai/forms/process' [POST,OPTIONS] to 'ai-form-process'
Mapped function route '/ai/generate-document' [POST,OPTIONS] to 'ai-generate-document'
Mapped function route '/ai/generate-outline' [POST,OPTIONS] to 'ai-generate-outline'
Mapped function route '/ai/improve-content' [POST,OPTIONS] to 'ai-improve-content'
Mapped function route '/ai/insights' [POST,OPTIONS] to 'ai-insights'
Mapped function route '/search/intelligent' [GET,POST,OPTIONS] to 'ai-intelligent-search'
Mapped function route '/ai/models/train' [POST,OPTIONS] to 'ai-model-train'
Mapped function route '/ai/operations/{operationId}/cancel' [POST,OPTIONS] to 'ai-operation-cancel'
Mapped function route '/ai/operations/{operationId}/retry' [POST,OPTIONS] to 'ai-operation-retry'
Mapped function route '/ai/operations' [POST,OPTIONS] to 'ai-operation-start'
Mapped function route '/ai/operations/{operationId}' [GET,OPTIONS] to 'ai-operation-status'
Mapped function route '/ai/operations/list' [GET,OPTIONS] to 'ai-operations-list'
Mapped function route '/ai/process' [POST,OPTIONS] to 'ai-process'
Mapped function route '/ai/summarize' [POST,OPTIONS] to 'ai-summarize'
Mapped function route '/ai/translate' [POST,OPTIONS] to 'ai-translate'
Mapped function route '/analytics' [POST,OPTIONS] to 'analytics-get'
Mapped function route '/analytics/process' [POST,OPTIONS] to 'analytics-process'
Mapped function route '/analytics/realtime' [GET,OPTIONS] to 'analytics-realtime'
Mapped function route '/api-keys' [POST,OPTIONS] to 'api-key-create'
Mapped function route '/api-keys/validate' [POST,OPTIONS] to 'api-key-validate'
Mapped function route '/infrastructure/api-keys' [POST,OPTIONS] to 'api-keys-create'
Mapped function route '/infrastructure/api-keys/list' [GET,OPTIONS] to 'api-keys-list'
Mapped function route '/audit/alerts' [GET,OPTIONS] to 'audit-alerts'
Mapped function route '/security/audit' [POST,OPTIONS] to 'audit-event-record'
Mapped function route '/audit/logs' [GET,OPTIONS] to 'audit-logs'
Mapped function route '/audit/metrics' [GET,OPTIONS] to 'audit-metrics'
Mapped function route '/infrastructure/auth/login' [POST,OPTIONS] to 'auth-login'
Mapped function route '/auth/sync' [POST,OPTIONS] to 'auth-sync'
Mapped function route '/system/backups' [POST,GET,OPTIONS] to 'backup-management'
Mapped function route '/system/cache' [POST,OPTIONS] to 'cache-management'
Mapped function route '/collaboration/sessions' [POST,OPTIONS] to 'collaboration-session-create'
Mapped function route '/collaboration/sessions/{sessionId}/join' [POST,OPTIONS] to 'collaboration-session-join'
Mapped function route '/security/compliance/assessment' [POST,OPTIONS] to 'compliance-assessment'
Mapped function route '/ai/content/generate' [POST,OPTIONS] to 'content-generate'
Mapped function route '/dashboards' [POST,OPTIONS] to 'dashboard-create'
Mapped function route '/dashboards/{dashboardId}' [GET,OPTIONS] to 'dashboard-get'
Mapped function route '/dashboard/metrics' [GET,OPTIONS] to 'dashboard-metrics'
Mapped function route '/dashboard/recent-activity' [GET,OPTIONS] to 'dashboard-recent-activity'
Mapped function route '/storage/encryption/decrypt' [POST,OPTIONS] to 'data-decryption'
Mapped function route '/storage/encryption/encrypt' [POST,OPTIONS] to 'data-encryption'
Mapped function route '/data/export' [POST,OPTIONS] to 'data-export'
Mapped function route '/storage/migration' [POST,OPTIONS] to 'data-migration'
Mapped function route '/mobile/devices/register' [POST,OPTIONS] to 'device-register'
Mapped function route '/mobile/devices/sync' [POST,OPTIONS] to 'device-sync'
Mapped function route '/documents/{documentId}/analytics' [GET,OPTIONS] to 'document-analytics'
Mapped function route '/documents/analyze' [POST,OPTIONS] to 'document-analyze'
Mapped function route '/documents/{documentId}/collaboration/end' [POST,OPTIONS] to 'document-collaboration-end'
Mapped function route '/documents/{documentId}/collaboration/start' [POST,OPTIONS] to 'document-collaboration-start'
Mapped function route '/documents/{documentId}/comments/{commentId}/delete' [DELETE,OPTIONS] to 'document-comment-delete'
Mapped function route '/documents/{documentId}/comments/create' [POST,OPTIONS] to 'document-comments-create'
Mapped function route '/documents/{documentId}/comments/list' [GET,OPTIONS] to 'document-comments-get'
Mapped function route '/documents/{documentId}/comments/{commentId}/update' [PUT,OPTIONS] to 'document-comments-update'
Mapped function route '/documents/{documentId}/download' [GET,OPTIONS] to 'document-download'
Mapped function route '/document-enhancement/categories' [POST,OPTIONS] to 'document-enhancement-categories-create'
Mapped function route '/document-enhancement/categories/list' [GET,OPTIONS] to 'document-enhancement-categories-list'
Mapped function route '/document-enhancement/operations' [GET,OPTIONS] to 'document-enhancement-operations-list'
Mapped function route '/document-enhancement/process' [POST,OPTIONS] to 'document-enhancement-process'
Mapped function route '/documents' [GET,OPTIONS] to 'document-list'
Mapped function route '/documents/{documentId}/metadata' [PUT,PATCH,OPTIONS] to 'document-metadata-update'
Mapped function route '/documents/{documentId}/process' [POST,OPTIONS] to 'document-process'
Mapped function route '/documents/{documentId}/process/stop' [POST,OPTIONS] to 'document-process-stop'
Mapped function route '/documents/{documentId}' [GET,OPTIONS] to 'document-retrieve'
Mapped function route '/documents/{documentId}/share' [POST,OPTIONS] to 'document-share'
Mapped function route '/documents/{documentId}/tags' [PUT,PATCH,OPTIONS] to 'document-tags-update'
Mapped function route '/documents/{documentId}/update' [PUT,PATCH,OPTIONS] to 'document-update'
Mapped function route '/documents/upload' [POST,OPTIONS] to 'document-upload'
Mapped function route '/documents/{documentId}/upload/complete' [POST,OPTIONS] to 'document-upload-complete'
Mapped function route '/documents/upload-image' [POST,OPTIONS] to 'document-upload-image'
Mapped function route '/communication/emails' [POST,OPTIONS] to 'email-send'
Mapped function route '/eventgrid/publish' [POST,OPTIONS] to 'event-grid-publish'
Mapped function route '/eventgrid/webhook' [POST,OPTIONS] to 'event-grid-webhook'
Mapped function route '/external-services/process' [POST,OPTIONS] to 'external-services-process'
Mapped function route '/system/feature-flags/evaluate' [GET,OPTIONS] to 'feature-flag-evaluate'
Mapped function route '/system/feature-flags' [GET,POST,PUT,DELETE,OPTIONS] to 'feature-flags'
Mapped function route '/infrastructure/templates/list' [GET,OPTIONS] to 'infrastructure-templates-list'
Mapped function route '/integrations' [POST,OPTIONS] to 'integration-create'
Mapped function route '/integrations/{integrationId}' [GET,OPTIONS] to 'integration-get'
Mapped function route '/integrations/list' [GET,OPTIONS] to 'integration-list'
Mapped function route '/webhooks/lemonsqueezy' [POST,OPTIONS] to 'lemonsqueezy-webhook'
Mapped function route '/infrastructure/logging' [POST,OPTIONS] to 'logging-entry'
Mapped function route '/communication/messages' [POST,OPTIONS] to 'message-send'
Mapped function route '/system/metrics' [POST,OPTIONS] to 'metrics-collect'
Mapped function route '/mobile/notifications/send' [POST,OPTIONS] to 'mobile-notification-send'
Mapped function route '/mobile/process' [POST,OPTIONS] to 'mobile-process'
Mapped function route '/analytics/models/train' [POST,OPTIONS] to 'model-train'
Mapped function route '/monitoring/alerts' [GET,OPTIONS] to 'monitoring-alerts'
Mapped function route '/monitoring/health' [GET,OPTIONS] to 'monitoring-health'
Mapped function route '/monitoring/performance' [GET,OPTIONS] to 'monitoring-performance'
Mapped function route '/notifications/analytics' [GET,OPTIONS] to 'notification-analytics'
Mapped function route '/notifications' [GET,OPTIONS] to 'notification-list'
Mapped function route '/notifications/mark-read' [POST,OPTIONS] to 'notification-mark-read'
Mapped function route '/notifications/preferences' [GET,PUT,PATCH,OPTIONS] to 'notification-preferences'
Mapped function route '/notifications/send' [POST,OPTIONS] to 'notification-send'
Mapped function route '/notifications/track' [POST,OPTIONS] to 'notification-tracking'
Mapped function route '/mobile/offline-data' [GET,OPTIONS] to 'offline-data'
Mapped function route '/organizations' [POST,OPTIONS] to 'organization-create'
Mapped function route '/organizations/{organizationId}' [GET,OPTIONS] to 'organization-get'
Mapped function route '/organizations/{organizationId}/members/invite' [POST,OPTIONS] to 'organization-invite-member'
Mapped function route '/organizations/list' [GET,OPTIONS] to 'organization-list'
Mapped function route '/organizations/members/{memberId}/activities' [GET,OPTIONS] to 'organization-member-activities'
Mapped function route '/organizations/{organizationId}/members/{userId}/role' [PUT,PATCH,OPTIONS] to 'organization-member-role-update'
Mapped function route '/organizations/{organizationId}/update' [PUT,PATCH,OPTIONS] to 'organization-update'
Mapped function route '/performance/metrics' [GET,OPTIONS] to 'performance-metrics'
Mapped function route '/analytics/predictions' [POST,OPTIONS] to 'prediction-generate'
Mapped function route '/projects/{projectId}/activity' [GET,OPTIONS] to 'project-activity'
Mapped function route '/projects/{projectId}/members' [POST,OPTIONS] to 'project-add-member'
Mapped function route '/projects' [POST,OPTIONS] to 'project-create'
Mapped function route '/projects/{projectId}/documents' [GET,OPTIONS] to 'project-documents'
Mapped function route '/projects/{projectId}' [GET,OPTIONS] to 'project-get'
Mapped function route '/projects/list' [GET,OPTIONS] to 'project-list'
Mapped function route '/projects/{projectId}/update' [PUT,PATCH,OPTIONS] to 'project-update'
Mapped function route '/communication/notifications/push' [POST,OPTIONS] to 'push-notification-send'
Mapped function route '/rag/query' [POST,OPTIONS] to 'rag-query'
Mapped function route '/infrastructure/rate-limit/check' [GET,POST,OPTIONS] to 'rate-limit-check'
Mapped function route '/reports/generate' [POST,OPTIONS] to 'reports-generate'
Mapped function route '/search' [GET,OPTIONS] to 'search'
Mapped function route '/search/advanced' [GET,POST,OPTIONS] to 'search-advanced'
Mapped function route '/search/analytics' [POST,OPTIONS] to 'search-analytics'
Mapped function route '/search/documents' [GET,OPTIONS] to 'search-documents'
Mapped function route '/storage/search' [POST,OPTIONS] to 'search-execute'
Mapped function route '/storage/search/index' [POST,OPTIONS] to 'search-index-document'
Mapped function route '/search/suggestions' [GET,POST,OPTIONS] to 'search-suggestions'
Mapped function route '/security/dashboard' [GET,OPTIONS] to 'security-dashboard'
Mapped function route '/security/events' [POST,OPTIONS] to 'security-event-record'
Mapped function route '/security/operations/{operationId}/status' [GET,OPTIONS] to 'security-operation-status'
Mapped function route '/security/scan' [POST,OPTIONS] to 'security-scan'
Mapped function route '/signalr/negotiate' [POST,OPTIONS] to 'signalr-negotiate'
Mapped function route '/ai/smart-form/process' [POST,OPTIONS] to 'smart-form-process'
Mapped function route '/ai/smart-form/templates' [GET,OPTIONS] to 'smart-form-templates'
Mapped function route '/storage/bulk/upload' [POST,OPTIONS] to 'storage-bulk-upload'
Mapped function route '/storage/configure' [POST,OPTIONS] to 'storage-configure'
Mapped function route '/storage/operations/{operationId}/status' [GET,OPTIONS] to 'storage-operation-status'
Mapped function route '/storage/sync' [POST,OPTIONS] to 'storage-sync'
Mapped function route '/subscriptions' [POST,OPTIONS] to 'subscription-create'
Mapped function route '/subscriptions/{subscriptionId}' [GET,OPTIONS] to 'subscription-get'
Mapped function route '/system/configuration' [GET,POST,PUT,DELETE,OPTIONS] to 'system-configuration'
Mapped function route '/system/health' [GET,OPTIONS] to 'system-health'
Mapped function route '/teams/{teamId}/members' [POST,OPTIONS] to 'team-member-add'
Mapped function route '/teams/{teamId}/members/{userId}/remove' [DELETE,OPTIONS] to 'team-member-remove'
Mapped function route '/teams/{teamId}/members/{userId}/update' [PUT,PATCH,OPTIONS] to 'team-member-update'
Mapped function route '/teams/{teamId}/members/list' [GET,OPTIONS] to 'team-members-list'
Mapped function route '/templates/categories' [GET,OPTIONS] to 'template-categories'
Mapped function route '/templates/{templateId}/clone' [POST,OPTIONS] to 'template-clone'
Mapped function route '/templates/create' [POST,OPTIONS] to 'template-create'
Mapped function route '/templates/{templateId}/delete' [DELETE,OPTIONS] to 'template-delete'
Mapped function route '/templates/{templateId}/download' [GET,OPTIONS] to 'template-download'
Mapped function route '/templates/generate' [POST,OPTIONS] to 'template-generate'
Mapped function route '/templates/{templateId}' [GET,OPTIONS] to 'template-get'
Mapped function route '/templates/{templateId}/preview' [POST,OPTIONS] to 'template-preview'
Mapped function route '/templates/{templateId}/share' [POST,OPTIONS] to 'template-share'
Mapped function route '/templates/sharing/entities' [GET,OPTIONS] to 'template-sharing-entities'
Mapped function route '/templates/{templateId}/sharing' [GET,OPTIONS] to 'template-sharing-get'
Mapped function route '/templates/{templateId}/update' [PUT,OPTIONS] to 'template-update'
Mapped function route '/infrastructure/templates' [POST,OPTIONS] to 'templates-create'
Mapped function route '/templates/list' [GET,OPTIONS] to 'templates-list'
Mapped function route '/users/{userId?}/activity' [GET,OPTIONS] to 'user-activity-get'
Mapped function route '/users/activity' [POST,OPTIONS] to 'user-activity-track'
Mapped function route '/users/{userId}' [GET,OPTIONS] to 'user-get'
Mapped function route '/users/{userId?}/permissions' [GET,OPTIONS] to 'user-permissions-get'
Mapped function route '/users/preferences' [GET,OPTIONS] to 'user-preferences-get'
Mapped function route '/users/preferences/reset' [POST,OPTIONS] to 'user-preferences-reset'
Mapped function route '/users/preferences/update' [PUT,PATCH,OPTIONS] to 'user-preferences-update'
Mapped function route '/users/{userId?}/profile' [GET,OPTIONS] to 'user-profile-get'
Mapped function route '/users/profile' [PUT,PATCH,OPTIONS] to 'user-profile-update'
Mapped function route '/users/tenant/switch' [POST,OPTIONS] to 'user-tenant-switch'
Mapped function route '/user/preferences/theme' [PUT,PATCH,OPTIONS] to 'user-theme-preferences'
Mapped function route '/users' [GET,OPTIONS] to 'users-list'
Mapped function route '/users/lookup' [POST,OPTIONS] to 'users-lookup'
Mapped function route '/webhooks/deliver' [POST,OPTIONS] to 'webhook-deliver'
Mapped function route '/workflows/analytics' [GET,OPTIONS] to 'workflow-analytics'
Mapped function route '/workflows' [POST,OPTIONS] to 'workflow-create'
Mapped function route '/workflows/execute' [POST,OPTIONS] to 'workflow-execute'
Mapped function route '/workflows/executions/{executionId}' [GET,OPTIONS] to 'workflow-execution-status'
Mapped function route '/workflows/{workflowId}/executions' [GET,OPTIONS] to 'workflow-executions'
Mapped function route '/workflows/{workflowId}' [GET,OPTIONS] to 'workflow-get'
Mapped function route '/workflows/list' [GET,OPTIONS] to 'workflow-list'
Mapped function route '/workflows/search/suggestions' [GET,OPTIONS] to 'workflow-search-suggestions'
Mapped function route '/workflow-templates' [POST,OPTIONS] to 'workflow-template-create'
Mapped function route '/workflows/all' [GET,OPTIONS] to 'workflows-list'