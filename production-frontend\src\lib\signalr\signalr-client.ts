/**
 * SignalR Client for Real-time Communication
 * Production-ready SignalR integration with Azure Functions backend
 */

import { HubConnection, HubConnectionBuilder, LogLevel, HubConnectionState } from '@microsoft/signalr'
import { useAuthStore } from '@/stores/auth-store'
import type { Document, UserContext } from '@/types/backend'

// SignalR Configuration
const SIGNALR_CONFIG = {
  hubUrl: process.env.NEXT_PUBLIC_SIGNALR_HUB_URL || 'http://localhost:7071/signalr/negotiate',
  reconnectAttempts: 5,
  reconnectDelay: 5000,
  connectionTimeout: 30000,
  keepAliveInterval: 15000,
}

// Event types for type safety
export interface SignalREvents {
  // Document events
  'DocumentUpdated': (document: Document) => void
  'DocumentDeleted': (documentId: string) => void
  'DocumentProcessingStarted': (documentId: string, operationId: string) => void
  'DocumentProcessingCompleted': (documentId: string, operationId: string, result: any) => void
  'DocumentProcessingFailed': (documentId: string, operationId: string, error: string) => void
  
  // Collaboration events
  'UserJoined': (sessionId: string, user: UserContext) => void
  'UserLeft': (sessionId: string, userId: string) => void
  'CursorMoved': (sessionId: string, userId: string, position: { x: number; y: number; page?: number }) => void
  'DocumentLocked': (documentId: string, userId: string, lockId: string) => void
  'DocumentUnlocked': (documentId: string, lockId: string) => void
  'CommentAdded': (documentId: string, comment: any) => void
  'CommentUpdated': (documentId: string, comment: any) => void
  'CommentDeleted': (documentId: string, commentId: string) => void
  
  // Notification events
  'NotificationReceived': (notification: any) => void
  'SystemAlert': (alert: any) => void
  
  // Connection events
  'Connected': () => void
  'Disconnected': (error?: Error) => void
  'Reconnecting': (error?: Error) => void
  'Reconnected': (connectionId?: string) => void
}

export type SignalREventName = keyof SignalREvents
export type SignalREventHandler<T extends SignalREventName> = SignalREvents[T]

/**
 * SignalR Client Manager
 */
export class SignalRClient {
  private connection: HubConnection | null = null
  private eventHandlers: Map<string, Set<Function>> = new Map()
  private reconnectAttempts = 0
  private isConnecting = false
  private connectionPromise: Promise<void> | null = null

  constructor() {
    this.setupConnection()
  }

  private setupConnection() {
    const hubUrl = SIGNALR_CONFIG.hubUrl
    
    this.connection = new HubConnectionBuilder()
      .withUrl(hubUrl, {
        accessTokenFactory: () => {
          const token = useAuthStore.getState().token
          return token?.accessToken || ''
        },
        withCredentials: false,
      })
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: (retryContext) => {
          if (retryContext.previousRetryCount < SIGNALR_CONFIG.reconnectAttempts) {
            return SIGNALR_CONFIG.reconnectDelay * Math.pow(2, retryContext.previousRetryCount)
          }
          return null // Stop reconnecting
        }
      })
      .configureLogging(
        process.env.NODE_ENV === 'development' ? LogLevel.Information : LogLevel.Warning
      )
      .build()

    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    if (!this.connection) return

    // Connection state events
    this.connection.onclose((error) => {
      console.warn('SignalR connection closed:', error)
      this.emit('Disconnected', error)
      this.reconnectAttempts = 0
    })

    this.connection.onreconnecting((error) => {
      console.log('SignalR reconnecting:', error)
      this.emit('Reconnecting', error)
    })

    this.connection.onreconnected((connectionId) => {
      console.log('SignalR reconnected:', connectionId)
      this.emit('Reconnected', connectionId)
      this.reconnectAttempts = 0
    })

    // Document events
    this.connection.on('DocumentUpdated', (document: Document) => {
      this.emit('DocumentUpdated', document)
    })

    this.connection.on('DocumentDeleted', (documentId: string) => {
      this.emit('DocumentDeleted', documentId)
    })

    this.connection.on('DocumentProcessingStarted', (documentId: string, operationId: string) => {
      this.emit('DocumentProcessingStarted', documentId, operationId)
    })

    this.connection.on('DocumentProcessingCompleted', (documentId: string, operationId: string, result: any) => {
      this.emit('DocumentProcessingCompleted', documentId, operationId, result)
    })

    this.connection.on('DocumentProcessingFailed', (documentId: string, operationId: string, error: string) => {
      this.emit('DocumentProcessingFailed', documentId, operationId, error)
    })

    // Collaboration events
    this.connection.on('UserJoined', (sessionId: string, user: UserContext) => {
      this.emit('UserJoined', sessionId, user)
    })

    this.connection.on('UserLeft', (sessionId: string, userId: string) => {
      this.emit('UserLeft', sessionId, userId)
    })

    this.connection.on('CursorMoved', (sessionId: string, userId: string, position: any) => {
      this.emit('CursorMoved', sessionId, userId, position)
    })

    this.connection.on('DocumentLocked', (documentId: string, userId: string, lockId: string) => {
      this.emit('DocumentLocked', documentId, userId, lockId)
    })

    this.connection.on('DocumentUnlocked', (documentId: string, lockId: string) => {
      this.emit('DocumentUnlocked', documentId, lockId)
    })

    this.connection.on('CommentAdded', (documentId: string, comment: any) => {
      this.emit('CommentAdded', documentId, comment)
    })

    this.connection.on('CommentUpdated', (documentId: string, comment: any) => {
      this.emit('CommentUpdated', documentId, comment)
    })

    this.connection.on('CommentDeleted', (documentId: string, commentId: string) => {
      this.emit('CommentDeleted', documentId, commentId)
    })

    // Notification events
    this.connection.on('NotificationReceived', (notification: any) => {
      this.emit('NotificationReceived', notification)
    })

    this.connection.on('SystemAlert', (alert: any) => {
      this.emit('SystemAlert', alert)
    })
  }

  async connect(): Promise<void> {
    if (this.isConnecting || this.connectionPromise) {
      return this.connectionPromise || Promise.resolve()
    }

    if (!this.connection) {
      this.setupConnection()
    }

    if (this.connection?.state === HubConnectionState.Connected) {
      return Promise.resolve()
    }

    this.isConnecting = true
    this.connectionPromise = Promise.race([
      this.connection!.start()
        .then(() => {
          console.log('SignalR connected successfully')
          this.emit('Connected')
          this.isConnecting = false
          this.reconnectAttempts = 0
        })
        .catch((error) => {
          console.error('SignalR connection failed:', error)
          this.isConnecting = false
          this.reconnectAttempts++
          throw error
        }),
      new Promise<void>((_, reject) => {
        setTimeout(() => {
          console.warn('SignalR connection timed out after 10 seconds')
          this.isConnecting = false
          reject(new Error('SignalR connection timed out'))
        }, 10000)
      })
    ])
//

    return this.connectionPromise
  }

  async disconnect(): Promise<void> {
    if (this.connection && this.connection.state === HubConnectionState.Connected) {
      await this.connection.stop()
    }
    this.connectionPromise = null
    this.isConnecting = false
  }

  // Event subscription methods
  on<T extends SignalREventName>(event: T, handler: SignalREventHandler<T>): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set())
    }
    this.eventHandlers.get(event)!.add(handler)
  }

  off<T extends SignalREventName>(event: T, handler: SignalREventHandler<T>): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.eventHandlers.delete(event)
      }
    }
  }

  private emit(event: string, ...args: any[]): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args)
        } catch (error) {
          console.error(`Error in SignalR event handler for ${event}:`, error)
        }
      })
    }
  }

  // Collaboration methods
  async joinCollaborationSession(sessionId: string): Promise<void> {
    if (this.connection?.state === HubConnectionState.Connected) {
      await this.connection.invoke('JoinCollaborationSession', sessionId)
    }
  }

  async leaveCollaborationSession(sessionId: string): Promise<void> {
    if (this.connection?.state === HubConnectionState.Connected) {
      await this.connection.invoke('LeaveCollaborationSession', sessionId)
    }
  }

  async updateCursor(sessionId: string, position: { x: number; y: number; page?: number }): Promise<void> {
    if (this.connection?.state === HubConnectionState.Connected) {
      await this.connection.invoke('UpdateCursor', sessionId, position)
    }
  }

  async lockDocument(documentId: string): Promise<string> {
    if (this.connection?.state === HubConnectionState.Connected) {
      return await this.connection.invoke('LockDocument', documentId)
    }
    throw new Error('SignalR not connected')
  }

  async unlockDocument(documentId: string, lockId: string): Promise<void> {
    if (this.connection?.state === HubConnectionState.Connected) {
      await this.connection.invoke('UnlockDocument', documentId, lockId)
    }
  }

  async addComment(documentId: string, comment: any): Promise<void> {
    if (this.connection?.state === HubConnectionState.Connected) {
      await this.connection.invoke('AddComment', documentId, comment)
    }
  }

  // Generic invoke method
  async invoke(method: string, ...args: any[]): Promise<any> {
    if (this.connection?.state === HubConnectionState.Connected) {
      return await this.connection.invoke(method, ...args)
    }
    throw new Error('SignalR not connected')
  }

  // Utility methods
  get isConnected(): boolean {
    return this.connection?.state === HubConnectionState.Connected
  }

  get connectionState(): HubConnectionState | null {
    return this.connection?.state || null
  }

  get connectionId(): string | null {
    return this.connection?.connectionId || null
  }
}

// Export singleton instance
export const signalRClient = new SignalRClient()

// Auto-connect when authenticated
if (typeof window !== 'undefined') {
  const authStore = useAuthStore.getState()
  
  // Connect when user is authenticated
  useAuthStore.subscribe((state) => {
    if (state.isAuthenticated && !signalRClient.isConnected) {
      signalRClient.connect().catch(console.error)
    } else if (!state.isAuthenticated && signalRClient.isConnected) {
      signalRClient.disconnect().catch(console.error)
    }
  })

  // Initial connection if already authenticated
  if (authStore.isAuthenticated) {
    signalRClient.connect().catch(console.error)
  }
}
